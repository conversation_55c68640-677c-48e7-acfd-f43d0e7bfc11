@description('Key Vault name for environment variables')
param keyVaultName string = '${baseName}-kv'

resource keyVault 'Microsoft.KeyVault/vaults@2023-02-01' = {
  name: keyVaultName
  location: location
  properties: {
    tenantId: subscription().tenantId
    sku: {
      family: 'A'
      name: 'standard'
    }
    accessPolicies: []
    enabledForDeployment: true
    enabledForTemplateDeployment: true
    enabledForDiskEncryption: true
  }
}
@description('Location for resources')
param location string = resourceGroup().location

@description('Base name prefix for resources')
param baseName string

@description('Deploy a new Storage Account if true; else use existing')
param deployStorage bool = true

@description('Existing Storage Account resource ID (when deployStorage=false)')
@allowed([ '' ])
param existingStorageId string = ''

@description('Deploy Container Apps Environment if true')
param deployCae bool = true

@description('Container Apps Environment name')
param caeName string = '${baseName}-cae'

@description('App container image (ACR or Docker Hub)')
param appImage string

@description('CPU cores for API/worker')
param cpu int = 2

@description('Memory in Gi for API/worker')
param memory string = '4Gi'

@description('Enable GPU? (Requires GPU capable env/node)')
param enableGpu bool = false


// In production, all environment variables and secrets should be stored in Key Vault.
// Remove parameters for env vars and secrets. Insert them into Key Vault manually or via automation.

var storageName = toLower(replace('${baseName}stor', '-', ''))

resource stg 'Microsoft.Storage/storageAccounts@2023-01-01' = if (deployStorage) {
  name: storageName
  location: location
  sku: {
    name: 'Standard_LRS'
  }
  kind: 'StorageV2'
  properties: {
    allowBlobPublicAccess: false
    minimumTlsVersion: 'TLS1_2'
  }
}

resource blob 'Microsoft.Storage/storageAccounts/blobServices@2023-01-01' = if (deployStorage) {
  name: 'default'
  parent: stg
  properties: {}
}

resource containerUploads 'Microsoft.Storage/storageAccounts/blobServices/containers@2023-01-01' = if (deployStorage) {
  name: 'uploads'
  parent: blob
  properties: {
    publicAccess: 'None'
  }
}

resource containerArtifacts 'Microsoft.Storage/storageAccounts/blobServices/containers@2023-01-01' = if (deployStorage) {
  name: 'artifacts'
  parent: blob
  properties: {
    publicAccess: 'None'
  }
}

resource qsvc 'Microsoft.Storage/storageAccounts/queueServices@2023-01-01' = if (deployStorage) {
  name: 'default'
  parent: stg
  properties: {}
}

resource queueJobs 'Microsoft.Storage/storageAccounts/queueServices/queues@2023-01-01' = if (deployStorage) {
  name: 'jobs'
  parent: qsvc
}

resource cae 'Microsoft.App/managedEnvironments@2023-05-01' = if (deployCae) {
  name: caeName
  location: location
  properties: {
    appLogsConfiguration: {
      destination: 'log-analytics'
      logAnalyticsConfiguration: {
        customerId: ''
        sharedKey: ''
      }
    }
  }
}

@description('API app name')
param apiAppName string = '${baseName}-api'

resource apiApp 'Microsoft.App/containerApps@2023-05-01' = {
  name: apiAppName
  location: location
  properties: {
    managedEnvironmentId: deployCae ? cae.id : resourceId('Microsoft.App/managedEnvironments', caeName)
    configuration: {
      activeRevisionsMode: 'Single'
      ingress: {
        external: true
        targetPort: 8080
        transport: 'auto'
      }
      // For production, consider referencing secrets from Azure Key Vault
      // Reference all required secrets from Key Vault. Add each secret name here as needed.
      secrets: [
        {
          name: 'azure-foundry-model-endpoint'
          value: '@Microsoft.KeyVault(SecretUri=${keyVault.properties.vaultUri}secrets/AZURE_FOUNDRY_MODEL_ENDPOINT)'
        }
        {
          name: 'azure-foundry-endpoint'
          value: '@Microsoft.KeyVault(SecretUri=${keyVault.properties.vaultUri}secrets/AZURE_FOUNDRY_ENDPOINT)'
        }
        {
          name: 'azure-foundry-api-key'
          value: '@Microsoft.KeyVault(SecretUri=${keyVault.properties.vaultUri}secrets/AZURE_FOUNDRY_API_KEY)'
        }
      ]
      registries: []
    }
    template: {
      containers: [
        {
          name: 'api'
          image: appImage
          resources: {
            cpu: cpu
            memory: memory
          }
          // All environment variables are referenced from Key Vault secrets
          env: [
            {
              name: 'AZURE_FOUNDRY_MODEL_ENDPOINT'
              secretRef: 'azure-foundry-model-endpoint'
            }
            {
              name: 'AZURE_FOUNDRY_ENDPOINT'
              secretRef: 'azure-foundry-endpoint'
            }
            {
              name: 'AZURE_FOUNDRY_API_KEY'
              secretRef: 'azure-foundry-api-key'
            }
          ]
        }
      ]
      scale: {
        minReplicas: 1
        maxReplicas: 3
      }
    }
  }
}

output storageAccountId string = deployStorage ? stg.id : existingStorageId
output containerAppsUrl string = apiApp.properties.configuration.ingress.fqdn
